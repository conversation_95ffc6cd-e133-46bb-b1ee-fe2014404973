// out: false
.imagesSliderBlock {
  position: relative;
  &.inview {
    .slider {
      visibility: visible;
    }
    .cols {
      &:before {
        .transform(scaleX(1));
        .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.85, 0, 0.15, 1));
      }
    }
  }
  .slider {
    position: relative;
    white-space: nowrap;
    visibility: hidden;
    .slide {
      margin-right: @vw20;
      width: (@vw200 * 3) + (@vw20 * 2);
      position: relative;
      .innerLink {
        overflow: hidden;
        position: relative;
        .transform(translate3d(0,0,0));
        top: 0;
        display: inline-block;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        color: @almostWhite;
        &:hover {
          .innerContent {
            .transform(translateY(0));
          }
        }
      }
      .innerContent {
        background: @almostBlack;
        padding: @vw30 @vw20;
        line-height: 1;
        color: @almostWhite;
        position: absolute;
        width: 100%;
        height: auto;
        left: 0;
        bottom: 0;
        .transform(translateY(100%));
        .transitionMore(transform, 0.6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
        .subTitle {
          line-height: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
  .sliderIndicator {
    width: calc(100% ~"-" @vw200 ~"-" @vw200 ~"-" @vw20 ~"-" @vw20);
    margin: 0 auto;
    margin-top: @vw100 + @vw20;
    display: block;
    height: 2px;
    background: rgba(245, 241, 234, .2);
    position: relative;
    .innerBar {
      position: absolute;
      top: 50%;
      .transform(translateY(-50%));
      left: 0;
      width: 0;
      height: @vw10;
      background: @almostWhite;
    }
  }
  .contentWrapper {
    height: 100%;
  }
  .imageWrapper {
    height: auto;
    position: relative;
    overflow: hidden;
    width: 100%;
    .innerImage {
      height: 0;
      .paddingRatio(1, 1);
      .item {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        .transitionMore(opacity, .45s, 0s, ease-in-out);
        &:not(:first-child) {
          clip-path: inset(100%);
          opacity: 1;
        }
        &.active {
          opacity: 1;
          // clip-path: inset(0 0 0 0);
        }
      }
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .text {
    margin-top: @vw30;
  }
}
@media all and (max-width: 1080px) {
  .imagesSliderBlock {
    .slider {
      .slide {
        margin-right: @vw20-1080;
        width: (@vw200-1080 * 3) + (@vw20-1080 * 2);
        .innerContent {
          padding: @vw30-1080 @vw20-1080;
        }
      }
    }
    .sliderIndicator {
      width: calc(100% ~"-" @vw200-1080 ~"-" @vw200-1080 ~"-" @vw20-1080 ~"-" @vw20-1080);
      margin-top: @vw100-1080 + @vw20-1080;
      .innerBar {
        height: @vw10-1080;
      }
    }
    .text {
      margin-top: @vw30-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .imagesSliderBlock {
    .slider {
      .slide {
        margin-right: @vw20-580;
        width: calc(100% ~"-" @vw40-580);
        .innerContent {
          padding: @vw30-580 @vw20-580;
        }
      }
    }
    .sliderIndicator {
      width: calc(100% ~"-" @vw40-580);
      margin-top: @vw100-580 + @vw20-580;
      .innerBar {
        height: @vw10-580;
      }
    }
    .text {
      margin-top: @vw30-580;
    }
  }
}

@media all and (max-width: 580px) {
  
}
