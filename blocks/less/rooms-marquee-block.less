// out: false
.roomsMarqueeBlock {
  height: calc(100vh ~"-" @vw50 ~"-" @vw50);
  opacity: 0;
  min-height: @vw100 * 6;
  position: relative;
  &.inview {
    opacity: 1;
    .transitionMore(opacity, .6s, .45s);
  }
  .backgrounds {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    &:after {
      content: '';
      background: rgba(15, 15, 14, .4);
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    .background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .textLink {
    position: absolute;
    bottom: @vw84;
    left: 50%;
    .transform(translateX(-50%));
    z-index: 10;
    color: @almostWhite;
    &:before, &:after {
      background: @almostWhite;
    }
  }
  .marquee {
    width: 100%;
    top: 50%;
    .transform(translatey(-50%));
    position: absolute;
    white-space: nowrap;
    .itemsContainer {
        display: inline-block;
        overflow: hidden;
        position: relative;
        vertical-align: middle;
        .item {
            display: inline-block;
            vertical-align: middle;
            margin: 0 @vw200 + @vw40;
            a {
                .transitionMore(opacity, .3s);
                cursor: pointer;
                &:hover {
                    opacity: .5;
                }
                * {
                    cursor: pointer;
                }
            }
            img {
                display: block;
                width: auto;
                height: @vw40;
                object-fit: contain;
            }
        }
    }
  }
  .hugeTitle {
    color: @almostWhite;
  }
  .sliderIndicator {
    position: absolute;
    top: @vw84;
    left: 50%;
    display: flex;
    .transform(translateX(-50%));
    align-items: center;
    gap: @vw20;
    color: @almostWhite;
    font-size: @vw14;
    z-index: 10;
    .current, .total {
      font-weight: 500;
    }
    .arrow {
      cursor: pointer;
      padding: @vw8;
      border-radius: 50%;
      .transitionMore(all, .3s);
      display: flex;
      align-items: center;
      justify-content: center;
      width: @vw32;
      height: @vw32;

      &:hover {
        background: rgba(245, 241, 234, .1);
        transform: scale(1.1);
      }

      &:active {
        transform: scale(0.95);
      }

      i {
        font-size: @vw16;
        color: @almostWhite;
        .transitionMore(color, .3s);
      }

      &.prev {
        margin-right: @vw60;
      }
      &.next {
        margin-left: @vw60;
      }
    }
    .indicator {
      width: @vw80;
      height: 2px;
      background: rgba(245, 241, 234, .3);
      position: relative;
      overflow: hidden;
      .innerBar {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: @almostWhite;
        transform-origin: left center;
        transform: scaleX(0);
      }
    }
  }
}
@media all and (max-width: 1080px) {
  
}

@media all and (max-width: 580px) {
 
}
