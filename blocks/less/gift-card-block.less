// out: false
.giftCardBlock {
  &.inview {
    .subTitle, .text, .imageWrapper {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .card {
    background: @primaryColorDark;
    padding: @vw40;
    position: relative;
    &:after {
      content: '';
      background: transparent;
      position: absolute;
      top: 50%;
      border: 1px solid @almostWhite;
      left: 50%;
      .transform(translate(-50%, -50%));
      height: calc(100% ~"-" @vw40);
      width: calc(100% ~"-" @vw40);
      pointer-events: none;
    }
  }
  .cols {
    margin-left: -@vw10;
    width: calc(100% ~"+" @vw20);
    .col {
      display: inline-block;
      vertical-align: middle;
      margin: 0 @vw10;
      width: calc(50% ~"-" @vw20);
      &.text {
        padding: 0 @vw60;
        text-align: center;
      }
    }
  }
  .subTitle, .text, .imageWrapper {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .imageWrapper {
    height: auto;
    position: relative;
    overflow: hidden;
    width: 100%;
    .innerImage {
      height: 0;
      .paddingRatio(1, 1);
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .text {
    margin-top: @vw30;
  }
  .textLink {
    margin-top: @vw45;
  }
}
@media all and (max-width: 1080px) {
  .giftCardBlock {
    .card {
      padding: @vw40-1080;
      &:after {
        height: calc(100% ~"-" @vw40-1080);
        width: calc(100% ~"-" @vw40-1080);
      }
    }
    .cols {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .col {
        margin: 0 @vw8-1080;
        width: calc(50% ~"-" @vw16-1080);
        &.text {
          padding: 0 @vw40-1080;
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-1080);
    }
    .text {
      margin-top: @vw30-1080;
    }
    .textLink {
      margin-top: @vw45-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .giftCardBlock {
    .card {
      padding: @vw40-580;
      &:after {
        height: calc(100% ~"-" @vw40-580);
        width: calc(100% ~"-" @vw40-580);
      }
    }
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &.text {
          padding: 0 @vw60-580;
        }
        &:not(:last-child){
          margin-bottom: @vw40-580;
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-580);
    }
    .text {
      margin-top: @vw30-580;
    }
    .textLink {
      margin-top: @vw45-580;
    }
  }
}

@media all and (max-width: 580px) {
}
