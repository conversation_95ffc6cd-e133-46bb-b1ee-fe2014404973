// out: false
body {
  &:has(.roomWrapper) {
    .textBlock {
      .title {
        &.bigger {
          font-size: @vw45;
        }
      }
    }
  }
}
.textBlock {
  text-align: center;
}

@media all and (max-width: 1080px) {
  
}

@media all and (max-width: 580px) {
  body {
    &:has(.roomWrapper) {
      .textBlock {
        .title {
          &.bigger {
            font-size: @vw45-580;
          }
        }
      }
    }
  }
}
