// out: false
.detailsBlock {
  &.inview {
    .col {
      opacity: 1;
      .transitionMore(opacity, .45s, .6s, ease-in-out);
      .stagger(3, 0.15, .6s);
    }
  }
  .cols {
    margin-left: -@vw10;
    width: calc(100% ~"+" @vw20);
    .col {
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw10;
      width: calc(37.5% ~"-" @vw20);
      padding-right: @vw200 + @vw20;
      &:nth-child(2) {
        width: calc(25% ~"-" @vw20);
        padding-right: @vw100;
      }
      strong {
        margin-bottom: @vw10;
      }
      p {
        &:not(:last-child) {
          margin-bottom: @vw30;
        }
      }
    }
  }

  .title {
    margin-bottom: @vw30;
  }

  .col {
    opacity: 0;
    .content {
      .textBlock {
        margin-bottom: @vw20;

        &:last-child {
          margin-bottom: 0;
        }

        ul, ol {
          margin: @vw12 0;
          padding-left: @vw20;

          li {
            font-size: @vw16;
            line-height: 1.6;
            color: @almostBlack;
            margin-bottom: @vw6;
          }
        }

        strong {
          font-weight: 600;
          color: @almostBlack;
        }

        em {
          font-style: italic;
        }
      }
    }
  }
}

@media all and (max-width: 1080px) {
  .detailsBlock {
    .contentWrapper {
      padding: 0 @vw50-1080;
    }

    .cols {
      grid-template-columns: 1fr;
      gap: @vw40;
    }
  }
}

@media all and (max-width: 580px) {
  .detailsBlock {
    .col {
      .subTitle {
        font-size: @vw20;
        margin-bottom: @vw16;
      }

      .content {
        .textBlock {
          p {
            font-size: @vw14;
          }

          h1, h2, h3, h4, h5, h6 {
            &.subTitle {
              font-size: @vw16;
              margin: @vw12 0 @vw6 0;
            }
          }

          ul, ol {
            li {
              font-size: @vw14;
            }
          }
        }
      }
    }
  }
}
