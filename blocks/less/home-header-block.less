// out: false
.homeHeaderBlock {
  margin-top: 0 !important;
  text-align: center;
  &.inview {
    .stickyWrapper {
      .stickyCols {
        opacity: 1;
        .transitionMore(opacity, .6s, .15s, ease-in-out);
        .row {
          .innerContent {
            .arrowDown {
              .transform(scaleY(1));
              .transitionMore(transform, .45s, .95s, ease-in-out);
            }
          }
        }
      }
    }
  }
  .logo {
    height: @vw100 * 3;
    svg {
      height: 100%;
      width: auto;
      display: block;
      margin: auto;
      object-fit: contain;
    }
  }
  .stickyWrapper {
    height: 200vh;
    position: relative;
    .stickyCols {
      opacity: 0;
      height: 100vh;
      left: 0;
      top: 0;
      width: 100%;
      display: flex;
      overflow: hidden;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding-top: @vw100 + @vw50;
    }
    .row {
      height: 0;
      &:has(.logo) {
        position: absolute;
      }
      .logo {
        transform-origin: bottom;
        .transform(translateY(calc(-100% ~"-" @vw40)));
        display: flex;
        gap: @vw100 + @vw17;
        align-items: center;
        justify-content: center;
      }
      .innerContent {
        .transform(translateY(@vw40));
        .title {
          margin-bottom: @vw20;
        }
        .arrowDown {
          display: block;
          margin: auto;
          width: @vw35;
          transform-origin: top;
          .transform(scaleY(0));
          .innerArrow {
            position: relative;
            animation: verticalWobble 1s linear infinite;
          }
          svg {
            width: 100%;
            height: auto;
            object-fit: contain;
          }
        }
      }
    }
    .imageRow {
      height: 0;
      position: relative;
      overflow: hidden;
      width: 100%;
      .imageWrapper {
        display: inline-block;
        position: absolute;
        width: 30%;
        height: 100%;
        left: 50%;
        top: 50%;
        .transform(translateY(-50%) translateX(-50%));
        overflow: hidden;
        transform-origin: center;
        img, video {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
        }
      }
    }
  }
  .detailsWrapper {
    padding: @vw100 + @vw30 0;
    background: @cremeWhite;
    .cols {
      display: flex;
      flex-direction: row;
      margin-left: -@vw8;
      width: calc(100% ~"+" @vw16);
      .col {
        display: inline-flex;
        vertical-align: top;
        padding: 0 @vw80;
        flex-direction: column;
        align-items: center;
        width: calc(33.3333% ~"-" @vw16);
      }
      .divider {
        height: auto;
        width: 1px;
        background: rgba(0, 0, 0, .1);
      }
      .title {
        margin-bottom: @vw34;
      }
      .text {
        margin-bottom: @vw40;
      }
      .textLink {
        margin-top: auto;
      }
    }
  }
}

@keyframes verticalWobble {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10%);
  }
}

@media all and (max-width: 1080px) {
  // all @vw values need to be responsive
  .homeHeaderBlock {
    .stickyWrapper {
      .stickyCols {
        padding-top: @vw100-1080;
        .row {
          .logo {
            height: @vw100-1080 * 3;
            gap: @vw100-1080 + @vw17-1080;
          }
          .innerContent {
            .title {
              margin-bottom: @vw20-1080;
            }
            .arrowDown {
              width: @vw35-1080;
            }
          }
        }
      }
    }
    .imageRow {
      .imageWrapper {
        width: 100%;
        height: auto;
        img, video {
          object-fit: contain;
        }
      }
    }
    .detailsWrapper {
      padding: @vw100-1080 + @vw30-1080 0;
      .cols {
        .divider {
          height: 1px;
          width: auto;
        }
      }
    }
  }
}