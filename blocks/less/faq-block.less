// out : false
.faqBlock {
  &.inview {
    .bigTitle, .text, .faqItems {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .cols {
    margin-left: -@vw10;
    width: calc(100% ~"+" @vw20);
    .col {
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw10;
      width: calc(50% ~"-" @vw20);
      &.textCol {
        padding-right: @vw200 + @vw20;
      }
    }
  }
  .bigTitle, .text, .faqItems {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .bigTitle {
    margin-bottom: @vw40;
  }
  .text {
    margin-top: @vw30;
  }
  .faqItems {
    .faqItem {
      position: relative;
      padding-bottom: @vw30;
      cursor: pointer;
      &.active {
        .question {
          &:after {
            .transform(translateY(-50%) rotate(0deg));
          }
        }
        .answer {
          max-height: 1000px;
        }
      }
      &:after {
        content: '';
        position: absolute;
        height: 2px;
        background: @almostBlack;
        width: 100%;
        left: 0;
        bottom: 0;
      }
      * {
        pointer-events: none;
      }
      .question {
        font-weight: bold;
        padding-top: @vw20;
        padding-right: @vw70;
        position: relative;
        &:before, &:after {
          content: '';
          position: absolute;
          height: 2px;
          width: @vw20;
          background: @almostBlack;
          top: 70%;
          left: auto;
          right: 0;
          .transform(translateY(-50%));
          .transitionMore(transform, 0.6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
        }
        &:after {
          .transform(translateY(-50%) rotate(90deg));
        }
        .subTitle {
          // semibold font weight
          font-weight: 600; 
        }
      }
      .answer {
        max-height: 0;
        overflow: hidden;
        .transition(.3s);
        .text {
          margin-top: @vw30;
        }
      }
      .text {
        margin-top: 0;
      }
    }
  }
}

@media all and (max-width: 1080px) {
  .faqBlock {
    .cols {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .col {
        margin: 0 @vw8-1080;
        width: calc(50% ~"-" @vw16-1080);
        &.textCol {
          padding-right: @vw40-1080;
        }
      }
    }
    .bigTitle, .text, .faqItems {
      transform: translateY(@vw20-1080);
    }
    .bigTitle {
      margin-bottom: @vw40-1080;
    }
    .text {
      margin-top: @vw30-1080;
    }
    .faqItems {
      .faqItem {
        padding-bottom: @vw30-1080;
        .question {
          padding-top: @vw20-1080;
          padding-right: @vw70-1080;
          &:before, &:after {
            width: @vw20-1080;
          }
        }
        .answer {
          .text {
            margin-top: @vw30-1080;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .faqBlock {
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &.textCol {
          padding-right: 0;
        }
        &:not(:last-child){
          margin-bottom: @vw40-580;
        }
      }
    }
    .bigTitle, .text, .faqItems {
      transform: translateY(@vw20-580);
    }
    .bigTitle {
      margin-bottom: @vw40-580;
    }
    .text {
      margin-top: @vw30-580;
    }
    .faqItems {
      .faqItem {
        padding-bottom: @vw30-580;
        .question {
          padding-top: @vw20-580;
          padding-right: @vw70-580;
          &:before, &:after {
            width: @vw20-580;
          }
        }
        .answer {
          .text {
            margin-top: @vw30-580;
          }
        }
      }
    }
  }
}