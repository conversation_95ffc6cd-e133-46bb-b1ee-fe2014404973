// out: false
.biggerImageTextBlock {
  &.inview {
    .subTitle, .text, .imageWrapper {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .cols {
    margin-left: -@vw10;
    width: calc(100% ~"+" @vw20);
    .col {
      display: inline-block;
      vertical-align: middle;
      margin: 0 @vw10;
      width: calc(60% ~"-" @vw20);
      &.text {
        padding: 0 @vw112 + @vw20;
        text-align: center;
        width: calc(40% ~"-" @vw20);
      }
      &:not(.text) {
        &:first-child {
          padding-left: @vw112 + @vw20;
        }
      }
    }
  }
  .subTitle, .text, .imageWrapper {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .imageWrapper {
    height: auto;
    position: relative;
    overflow: hidden;
    width: 100%;
    .innerImage {
      height: 0;
      .paddingRatio(1080, 640);
      .innerTransform {
        position: absolute;
        top: -10;
        left: 0;
        width: 100%;
        height: 120%;
      }
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .text {
    margin-top: @vw30;
  }
}
@media all and (max-width: 1080px) {
  .biggerImageTextBlock {
    .cols {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .col {
        margin: 0 @vw8-1080;
        width: calc(50% ~"-" @vw16-1080);
        &.text {
          padding: 0 @vw40-1080;
        }
        &:not(.text) {
          &:first-child {
            padding-left: @vw50-1080;
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-1080);
    }
    .subTitle {
      margin-bottom: @vw20-1080;
    }
    .text {
      margin-top: @vw30-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .biggerImageTextBlock {
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &.text {
          padding: 0;
          text-align: center;
        }
        &:not(:last-child){
          margin-bottom: @vw40-580;
        }
        &:not(.text) {
          &:first-child {
            padding-left: 0;
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-580);
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .text {
      margin-top: @vw30-580;
    }
  }
}

@media all and (max-width: 580px) {
}
