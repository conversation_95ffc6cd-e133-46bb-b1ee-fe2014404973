// out: false
body {
  &:has(.contactBlock) {
    .footer, .whiteSpaceWrapper {
      display: none !important;
    }
    #header {
      background-color: @almostBlack;
      color: @almostWhite;
      .logo {
        svg {
          path, rect {
            fill: @almostWhite;
          }
        }
      }
      .hamburger {
        border-color: @almostWhite;
        .border {
          background: @almostWhite;
        }
      }
      .button {
        color: @almostWhite;
        border-color: @almostWhite;
        &:hover {
          color: @almostBlack;
          background: @almostWhite;
        }
      }
    }
    #pageContainer {
      background: @almostBlack;
    }
  }
}
.wpcf7-not-valid-tip, .wpcf7 form.invalid .wpcf7-response-output, .wpcf7 form.unaccepted .wpcf7-response-output, .wpcf7 form.payment-required .wpcf7-response-output, .wpcf7 form.sent .wpcf7-response-output {
  background: rgba(229, 93, 45, .3);
  border: none;
  color: @primaryColor;
  text-transform: none;
  padding: @vw14;
  margin-top: @vw10;
}
.wpcf7 form.sent .wpcf7-response-output {
  color: @almostWhite;
  background: rgba(245, 241, 234, .3);
}
.contactBlock {
  background: @almostBlack;
  margin-top: 0;
  margin-bottom: 0;
  padding-bottom: @vw100 + @vw80;
  color: @almostWhite;
  opacity: 0;
  &.inview {
    opacity: 1;
    .transitionMore(opacity, .6s, 0s, ease-in-out);
    .titleWrapper {
      opacity: 1;
      .filter(blur(0));
      .transitionMore(all, .6s, .9s, ease-in-out);
    }
    .backgroundImage {
      opacity: 1;
      .transitionMore(opacity, .6s, .15s, ease-in-out);
    }
  }
  label {
    color: @almostWhite;
    display: block;
    text-transform: uppercase;
    margin-bottom: @vw30;
    letter-spacing: .12rem;
    font-size: @vw18;
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .titleWrapper {
    position: absolute;
    bottom: @vw40;
    left: 0;
    opacity: 0;
    .filter(blur(10px));
    width: 100%;
    .hugeTitle {
      color: @almostWhite;
    }
  }
  .text {
    margin-top: @vw30;
  }
  .fluentform .ff-el-group {
    margin-bottom: 0;
  }
  .backgroundImage {
      width: 100%;
      position: relative;
      overflow: hidden;
      opacity: 0;
      height: auto;
      .innerImage {
        width: 100%;
        height: 0;
        .paddingRatio(1920,861);
        img, video {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
        }
      }
    }
    .tripAdvisor {
      display: inline-block;
      width: @vw65;
      height: @vw65;
      margin-top: @vw120;
      cursor: pointer;
      .transitionMore(opacity, .3s, 0s, ease-in-out);
      &:hover {
        opacity: .5;
      }
      img {
        pointer-events: none;
        width: 100%;
        height: auto;
      }
    }
    .socials {
      margin: @vw50 0 @vw20 0;
      .social {
        font-size: @vw33;
        &:not(:last-child) {
          margin-right: @vw30;
        }
      }
    }
    .innerCol {
      &:first-child {
        margin-bottom: @vw70;
      }
    }
  .contentWrapper {
    .cols {
      margin-top: @vw100 + @vw50;
      .col {
        vertical-align: top;
        display: inline-block;
        position: relative;
        width: 50%;
        &:first-child {
          padding-right: @vw100 + @vw66;
          &:before {
            content: '';
            width: 1px;
            height: 100%;
            background: @almostWhite;
            position: absolute;
            top: 0;
            right: @vw80;
          }
        }
        &:last-child {
          padding-left: @vw8;
        }

        .fluentform .ff-el-input--label.ff-el-is-required.asterisk-right label:after {
          color: @primaryColor;
        }
        
        .formWrapper {
          margin-top: @vw20;
          form {
            .field {
              margin: 0 @vw8;
              display: inline-block;
              width: calc(100% ~"-" @vw16);
              vertical-align: top;
              &.half {
                width: calc(50% ~"-" @vw16);
              }
              &.small {
                width: calc(25% ~"-" @vw16);
              }
              &.big {
                width: calc(75% ~"-" @vw16);
              }
            }

            select {
              appearance: none; /* Verbergt de standaard dropdown-pijl */
              -webkit-appearance: none;
              -moz-appearance: none;
              background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="gray"><path d="M5 7l5 5 5-5H5z"/></svg>');
              background-repeat: no-repeat;
              background-position: right 10px center;
              background-size: 16px;
              &.select2-hidden-accessible {
                width: 100%;
              }
            }

            input, select, textarea {
              width: 100%;
              padding: @vw16;
              border: 1px solid @almostWhite;
              .rounded(@vw10);
              font-size: @vw18;
              font-weight: 100;
              font-family: 'Figtree', Arial, sans-serif;
              box-sizing: border-box;
              background: transparent;
              margin-top: @vw20;
              letter-spacing: 0.1rem;
              color: @almostWhite;
              &:focus {
                border-color: none;
                outline: none;
              }

              &::-webkit-input-placeholder {
                color: rgba(@almostWhite, .7);
              }

              &:-moz-placeholder {
                color: rgba(@almostWhite, .7);
              }

              &::-moz-placeholder {
                color: rgba(@almostWhite, .7);
              }

              &:-ms-input-placeholder {
                color: rgba(@almostWhite, .7);
              }

              &::placeholder {
                color: rgba(@almostWhite, .7);
              }

            }

            select {
              color: rgba(@almostWhite, .7);
            }

            textarea {
              resize: none;
              height: @vw100 + @vw80;
            }

            input[type="submit"] {
              background-color: @almostWhite;
              font-weight: 500;
              height: @vw51;
              line-height: @vw51;
              color: @almostBlack;
              padding: 0 @vw50;
              display: block;
              width: auto;
              font-size: @vw18;
              .rounded(@vw10);
              cursor: pointer;
              .transition(.3s);
              -webkit-appearance: none;
              -moz-appearance: none;
              appearance: none;
            }
            .select2-container--default .select2-selection--single {
              height: @vw40;
              border: 1px solid #ddd;
              .rounded(@vw10);
            }

            .select2-container .select2-selection--single .select2-selection__rendered {
              padding-left: @vw10;
              line-height: 1;
            }

            .select2-container .select2-selection--single .select2-selection__arrow {
              height: @vw40;
              right: @vw10;
            }
            .error {
              color: #ff0000;
              background-color: #ffe6e6;
              border: 1px solid #ff0000;
              padding: 10px;
              .rounded(@vw10);
              margin-top: 5px;
              font-family: 'Figtree', Arial, sans-serif;
              font-size: @vw12;
              display: block;
            }
          }
        }
      }
    }
  }
  .link {
    color: @almostWhite;
    &:before, &:after {
      background: @almostWhite;
    }
  }
}

@media all and (max-width: 1080px) {
  .wpcf7-not-valid-tip,
  .wpcf7 form.invalid .wpcf7-response-output,
  .wpcf7 form.unaccepted .wpcf7-response-output,
  .wpcf7 form.payment-required .wpcf7-response-output,
  .wpcf7 form.sent .wpcf7-response-output {
    padding: @vw14-1080;
    margin-top: @vw10-1080;
  }

  .contactBlock {
    padding-bottom: @vw100-1080 + @vw80-1080;

    label {
      margin-bottom: @vw30-1080;
      font-size: @vw18-1080;
    }

    .subTitle {
      margin-bottom: @vw20-1080;
    }

    .titleWrapper {
      bottom: @vw40-1080;
    }

    .text {
      margin-top: @vw30-1080;
    }

    .tripAdvisor {
      width: @vw65-1080;
      height: @vw65-1080;
      margin-top: @vw120-1080;
    }

    .socials {
      margin: @vw50-1080 0 @vw20-1080 0;

      .social {
        font-size: @vw33-1080;

        &:not(:last-child) {
          margin-right: @vw30-1080;
        }
      }
    }

    .innerCol {
      &:first-child {
        margin-bottom: @vw70-1080;
      }
    }

    .contentWrapper {
      .cols {
        margin-top: @vw100-1080 + @vw50-1080;

        .col {
          &:first-child {
            padding-right: @vw100-1080 + @vw66-1080;

            &:before {
              right: @vw80-1080;
            }
          }

          &:last-child {
            padding-left: @vw8-1080;
          }

          .formWrapper {
            margin-top: @vw20-1080;

            form {
              .field {
                margin: 0 @vw8-1080;
                width: calc(100% ~"-" @vw16-1080);

                &.half {
                  width: calc(50% ~"-" @vw16-1080);
                }

                &.small {
                  width: calc(25% ~"-" @vw16-1080);
                }

                &.big {
                  width: calc(75% ~"-" @vw16-1080);
                }
              }

              input,
              select,
              textarea {
                padding: @vw16-1080;
                .rounded(@vw10-1080);
                font-size: @vw18-1080;
                margin-top: @vw20-1080;
              }

              textarea {
                height: @vw100-1080 + @vw80-1080;
              }

              input[type="submit"] {
                height: @vw51-1080;
                line-height: @vw51-1080;
                padding: 0 @vw50-1080;
                font-size: @vw18-1080;
                .rounded(@vw10-1080);
              }

              .select2-container--default .select2-selection--single {
                height: @vw40-1080;
                .rounded(@vw10-1080);
              }

              .select2-container .select2-selection--single .select2-selection__rendered {
                padding-left: @vw10-1080;
              }

              .select2-container .select2-selection--single .select2-selection__arrow {
                height: @vw40-1080;
                right: @vw10-1080;
              }

              .error {
                .rounded(@vw10-1080);
                font-size: @vw12-1080;
              }
            }
          }
        }
      }
    }
  }
}
