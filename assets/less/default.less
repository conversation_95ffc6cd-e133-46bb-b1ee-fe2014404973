// out: false
* {
  box-sizing: border-box;
  cursor:default;
  letter-spacing: 0;
  margin:0;
  padding:0;
  &::selection {
    background: @primaryColor;
    color: @hardWhite;
  }
  &::-webkit-selection {
    background: @primaryColor;
    color: @hardWhite;
  }
}

*:focus{
  outline:none;
}

html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}
html {
  overflow-x: hidden;
}
body {
  background: @almostWhite;
  color: @hardBlack;
  font-family: 'Figtree', arial, sans-serif;
  overflow-x: hidden;
  font-size: @vw20;
  width: 100vw;
  line-height: 1.4;
  .text {
    &.white {
      p {
        color: @almostWhite;
      }
      a {
        color: @almostWhite;
        &:before, &:after {
          background: @almostWhite;
        }
      }
    }
  }
  #cmplz-cookiebanner-container {
    display: none;
    opacity: 0;
  }
  > .flatpickr-calendar {
    display: none;
  }
  .cmplz-cookiebanner.cmplz-dismissed {
    display: none !important;
  }
  p {
    font-family: 'Figtree', arial, sans-serif;
    font-size: @vw20;
    color: @hardBlack;
    font-weight: 400;
    line-height: 1.5;
    letter-spacing: 0.01rem;
    a {
      color: @primaryColor;
      cursor: pointer;
      text-decoration: none;
      font-weight: 400;
    }
  }
  strong {
    font-size: @vw18;
    line-height: 1.2;
    font-family: "Figtree", sans-serif;
    font-weight: 200;
    letter-spacing: .125rem;
    font-style: normal;
    text-transform: uppercase;
  }
  a {
    cursor: pointer;
    text-decoration: none;
    * {
      cursor: pointer;
    }
  }
}

#pageContainer {
  background: @almostWhite;
  // padding-bottom: @vw100 + @vw100 + @vw40;
  display: block;
  position: relative;
  z-index: 1;
}

[data-scroll-section] {
  background: @hardWhite;
  // padding: @vw100 + @vw30 + @vw5 0 0 0;
}

[data-scroll-container] {
  position: absolute;
  top: 0;
  width: 100%;
}

.blocks {
  padding-bottom: 0.1px;
}

.roomWrapper {
  padding-bottom: @vw100 + @vw40;
  opacity: 0;
  &.inview {
    opacity: 1;
  }
  section {
    &:last-child {
      margin-bottom: 0;
    }
  }
}

section {
  margin: @vw100 + @vw100 + @vw40 0;
  &.noMarginTop {
    margin-top: 0;
  }
  &.noMarginBottom {
    margin-top: 0;
  }
  &.noBorderTop {
    &.black {
      &:before {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
      }
    }
  }
  &.noBorderBottom {
    &.black {
      &:before {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
  }
  &.dark {
    position: relative;
  }
  &.grey {
    margin: 0;
    position: relative;
    padding: @vw100 + @vw70 0;
    &:before {
      content: '';
      border-radius: @vw20;
      position: absolute;
      height: 100%;
      width: 100%;
      top: 0;
      left: 0;
      background: @cremeWhite;
    }
  }
}

.contentWrapper {
  display: block;
  width: 100vw;
  padding: 0 @vw90;
  margin: auto;
  &.small {
    padding: 0 @vw200 + @vw20 + @vw90;
  }
  &.smaller {
    padding: 0 (@vw200 * 2) + (@vw20 * 2) + @vw90;
  }
}

[data-split], [data-init] {
  // will-change: transform;
}

[data-split] {
  opacity: 0;
  &.inview {
    opacity: 1;
  }
}

.button {
  font-family: 'Figtree';
  font-size: @vw18;
  font-weight: 400;
  height: @vw47;
  line-height: @vw47;
  background: @almostWhite;
  color: @almostBlack;
  border: 1px solid @almostWhite;
  display: inline-block;
  padding: 0 @vw30;
  padding: 0 @vw30;
  text-transform: uppercase;
  transition: background .3s, color .3s;
  -webkit-appearance: none;
  letter-spacing: .12rem;
  &:hover {
    color: @almostWhite;
    background: @almostBlack;
  }
}

.textLink {
  text-decoration: none;
  font-family: 'Figtree';
  font-size: @vw18;
  letter-spacing: .12rem;
  color: @almostBlack;
  display: inline-block;
  position: relative;
  text-transform: uppercase;
  &:hover {
    &:before {
      .transform(scale(1));
      .transitionMore(transform, 0.6s, .15s, cubic-bezier(0.87, 0, 0.13, 1));
    }
    &:after {
      .transform(scale(0));
      .transitionMore(transform, 0.6s, 0s, cubic-bezier(0.87, 0, 0.13, 1));
    }
  }
  span {
    display: block;
    &:not(:last-child) {
      margin-bottom: @vw5;
    }
  }
  &:before {
    transform-origin: left;
    .transform(scale(0));
  }
  &:before, &:after {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 1px;
    background: @almostBlack;
    content: '';
  }
  &:after {
    transform-origin: right;
  }
  .innerText {
    letter-spacing: .125rem;
    .transition(.3s);
  }
}

.link {
  text-decoration: none;
  display: inline-block;
  position: relative;
  &:hover {
    &:before {
      .transform(scale(1));
      .transitionMore(transform, 0.6s, .15s, cubic-bezier(0.87, 0, 0.13, 1));
    }
    &:after {
      .transform(scale(0));
      .transitionMore(transform, 0.6s, 0s, cubic-bezier(0.87, 0, 0.13, 1));
    }
  }
  &:before {
    transform-origin: left;
    .transform(scale(0));
  }
  &:before, &:after {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 1px;
    background: @almostBlack;
    content: '';
  }
  &:after {
    transform-origin: right;
  }
}

.social {
  color: @almostWhite;
  .transitionMore(opacity, .3s, 0s, ease-in-out);
  &:hover {
    opacity: .7;
  }
}

.imageItem {
  max-width: 100%;
  height: auto;
  position: absolute;
  overflow: hidden;

  &.inview {
    .innerImage {
      .innerMask {
        height: 100%;
        width: 100%;
        transition: width 0.8s cubic-bezier(0.83, 0, 0.17, 1),
                    height 0.8s cubic-bezier(0.83, 0, 0.17, 1);

        img {
          transform: scale(1);
          transition: transform 0.8s cubic-bezier(0.83, 0, 0.17, 1);
        }
      }
    }
  }

  .innerImage {
    height: 0;
    padding-bottom: 100%;
    position: relative;  /* Added to ensure proper layout */

    .innerMask {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: @vw20;
      overflow: hidden;
      width: 0;
      height: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      transition: width 0.8s cubic-bezier(0.83, 0, 0.17, 1),
                  height 0.8s cubic-bezier(0.83, 0, 0.17, 1);

      img {
        position: absolute;
        object-fit: cover;
        object-position: center;
        margin: auto;
        display: block;
        transform: scale(2);
        transition: transform 0.8s cubic-bezier(0.83, 0, 0.17, 1);
        width: 100%;
        height: 100%;
      }
    }
  }
}

.cmplz-body {
  p {
    font-size: @vw14;
  }
}

@keyframes rotate360 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media screen and (max-width: 1080px) {
  body {
    font-size: @vw22-1080;
    .text {
      &.smaller {
        p {
          font-size: @vw16-1080;
        }
      }
    }
    p {
      font-size: @vw22-1080;
    }
  }

  section {
    margin: @vw100-1080 + @vw100-1080 0;
    &:first-child {
      padding-top: @vw100-1080;
    }
    &.black {
      padding: @vw100-1080 + @vw50-1080 0;
      &.first {
        padding-top: @vw100-1080 + @vw50-1080;
        &:before {
          border-radius: @vw20-1080 @vw20-1080 0 0;
        }
      }
      &:not(.last) {
        padding-bottom: 0;
      }
      &.last {
        &:before {
          border-radius: 0 0 @vw20-1080 @vw20-1080;
        }
      }
      &:before {
        // border-radius: @vw20-1080;
      }
    }
  }

  .contentWrapper {
    padding: 0 @vw50-1080;
    &.small {
      padding: 0 @vw112-1080;
    }
    &.smaller {
      padding: 0 @vw112-1080;
    }
  }

  .button {
    font-size: @vw22-1080;
    padding: 0 @vw20-1080;
    letter-spacing: .125rem;
    height: @vw47-1080;
    line-height: @vw44-1080;
  }

  // .textLink {
  //   font-size: @vw22-1080;
  //   span:not(:last-child) {
  //     margin-bottom: @vw5-1080;
  //   }
  //   .innerText {
  //     padding-right: @vw10-1080;
  //   }
  //   .arrow {
  //     height: @vw15-1080;
  //     margin: @vw10-1080 auto 0 auto;
  //     &:after {
  //       height: @vw15-1080;
  //       width: @vw15-1080;
  //     }
  //   }
  // }

  .cmplz-body {
    p {
      font-size: @vw14-1080;
    }
  }
}

@media screen and (max-width: 580px) {
  
}


