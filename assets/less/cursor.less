// out: false

// Custom Cursor Styles
.cursor,
.cursor-border {
  position: fixed;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  will-change: transform;
}

.cursor {
  width: @vw6;
  height: @vw6;
  background-color: @hardWhite;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  mix-blend-mode: difference;
}

.cursor-border {
  width: @vw50;
  height: @vw50;
  border: 1px solid @hardWhite;
  .rounded(50%);
  background-color: transparent;
  transform: translate(-50%, -50%);
  mix-blend-mode: difference;
}

// Hide cursor on touch devices
@media (hover: none) and (pointer: coarse) {
  .cursor,
  .cursor-border {
    display: none !important;
  }
}

// Cursor hover class for elements that should trigger hover effect
.cursor-hover {
  cursor: none;
}

// Hide default cursor on interactive elements when custom cursor is active
body:not(.touch-device) {
  a,
  button,
  [role="button"],
  .btn,
  input,
  textarea,
  select {
    cursor: none;
  }
}

// Special cursor states
.cursor-text {
  .cursor {
    width: 2px;
    height: 20px;
    border-radius: 1px;
  }
  
  .cursor-border {
    width: 20px;
    height: 40px;
    border-radius: 10px;
  }
}

.cursor-loading {
  .cursor {
    animation: cursorPulse 1s ease-in-out infinite;
  }
  
  .cursor-border {
    animation: cursorBorderSpin 2s linear infinite;
  }
}

@keyframes cursorPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.5);
  }
}

@keyframes cursorBorderSpin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// Dark mode cursor (if needed)
.dark-mode {
  .cursor {
    background-color: @almostWhite;
  }

  .cursor-border {
    border-color: @almostWhite;
  }
}

@media all and (max-width: 580px) {
  .cursor {
    width: @vw6-580;
    height: @vw6-580;
  }

  .cursor-border {
    width: @vw50-580;
    height: @vw50-580;
  }
}

