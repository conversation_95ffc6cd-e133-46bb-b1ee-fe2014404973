// out: ../../style.css, compress: true, strictMath: true

/*
Theme Name: THE FIG
Author: <PERSON>
Version: 1.0.0
*/

@import 'vw_values.less';
@import 'constants.less';
@import 'default.less';
@import 'typo.less';
@import 'cursor.less';
@import 'parts/header.less'; 
@import 'parts/header.less';
@import 'parts/footer.less';
@import 'parts/overlay.less';
@import 'parts/popup.less';
@import 'parts/booking-popup.less';
@import 'parts/sticky-whatsapp.less';
@import 'parts/ddsignature.less';

// blocks
@import '../../blocks/less/header-block.less';
@import '../../blocks/less/home-about-block.less';
@import '../../blocks/less/home-header-block.less';
@import '../../blocks/less/image-text-block.less';
@import '../../blocks/less/bigger-image-text-block.less';
@import '../../blocks/less/text-block.less';
@import '../../blocks/less/sticky-big-media-block.less';
@import '../../blocks/less/images-text-block.less';
@import '../../blocks/less/rooms-marquee-block.less';
@import '../../blocks/less/rooms-overview-block.less';
@import '../../blocks/less/gift-card-block.less';
@import '../../blocks/less/articles-block.less';
@import '../../blocks/less/partners-marquee-block.less';
@import '../../blocks/less/faq-block.less';
@import '../../blocks/less/four-images-text-block.less';
@import '../../blocks/less/images-slider-block.less';
@import '../../blocks/less/big-header-block.less';
@import '../../blocks/less/contact-block.less';
@import '../../blocks/less/pdf-menus-block.less';
@import '../../blocks/less/two-images-block.less';
@import '../../blocks/less/details-block.less';

@font-face {
  font-family: 'Figtree';
  src:  url('assets/fonts/figtree.woff2') format('woff2'),
    url('assets/fonts/figtree.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: block;
}
// include figtree (variabele font)
@font-face {
  font-family: 'Figtree';
  src: url('assets/fonts/Figtree-VariableFont_wght.woff2') format('woff2-variations'),
       url('assets/fonts/Figtree-VariableFont_wght.woff') format('woff-variations');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}
// include tartuffo:
@font-face {
  font-family: 'Tartuffo_Trial';
  src:  url('assets/fonts/tartuffo.woff2') format('woff2'),
    url('assets/fonts/tartuffo.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

@font-face {
  font-family: 'icomoon';
  src:  url('assets/fonts/icomoon.eot?qwq3hi');
  src:  url('assets/fonts/icomoon.eot?qwq3hi#iefix') format('embedded-opentype'),
    url('assets/fonts/icomoon.ttf?qwq3hi') format('truetype'),
    url('assets/fonts/icomoon.woff?qwq3hi') format('woff'),
    url('assets/fonts/icomoon.svg?qwq3hi#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-pinterest:before {
  content: "\e903";
}
.icon-arrow-up:before {
  content: "\e904";
}
.icon-arrow-down:before {
  content: "\e906";
}
.icon-print:before {
  content: "\e90d"; 
}
.icon-tiktok:before { 
  content: "\e900";
}
.icon-facebook:before {
  content: "\e901";
}
.icon-linkedin:before {
  content: "\e902";
}
.icon-youtube:before {
  content: "\e905";
}
.icon-instagram:before {
  content: "\e907";
}
.icon-arrow-left:before {
  content: "\e909";
}
.icon-arrow-right:before {
  content: "\e90a";
}
.icon-phone:before {
  content: "\e908";
}
.icon-whatsapp:before {
  content: "\e90b";
}
.icon-envelope:before {
  content: "\e90c";
}


::-webkit-scrollbar {
  width: @vw10;
}

::-webkit-scrollbar-track {
  background: @almostWhite;
}

::-webkit-scrollbar-thumb {
  border-radius: @vw50;
  background: rgba(0,0,0,.1);
}

.block__headline {
    padding: 20px 15px 30px;
    background: #fafafa;
    text-align: center;
}
.block__headline-title {
    font-family: 'Arial', sans-serif;
    font-size: 30px;
    font-weight: bold;
    position: relative;
}
.block__headline-title:after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background: #333;
    margin: 0 auto;
}

html.has-scroll-smooth {
	backface-visibility: hidden;
	transform: translateZ(0);
  [data-load-container] {
  	position: fixed;
  	top: 0;
  	right: 0;
  	bottom: 0;
  	left: 0;
  	width: 100vw;
  }
}

// Swup page transition animations - Snelle fade transitie
html.is-leaving #pageContainer {
  opacity: 0;
  transition: opacity 0.2s ease-out;
}

#pageContainer {
  opacity: 1;
  transition: opacity 0.2s ease-in-out;
}

// Accessibility
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

a {
  cursor: none !important;
  * {
    pointer-events: none;
  }
}

@media all and (max-width: 1080px) {

}

@media all and (max-width: 580px) {

}
